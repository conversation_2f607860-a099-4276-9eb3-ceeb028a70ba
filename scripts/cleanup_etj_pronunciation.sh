#!/bin/bash

cd /Users/<USER>/Videos/Courses/ETJ_English_The_British_Pronunciation_Programme_2020 || { echo "Target directory does not exist"; exit 1; }

# Delete files matching the pattern SHAREWOOD*.url
find . -name "SHAREWOOD*.url" -type f -delete

# Rename files and directories by removing the prefix [SW.BAND] only if basename starts with it and is not '.'
find . -depth -print0 | while IFS= read -r -d $'\0' item; do
  base="$(basename "$item")"
  dir="$(dirname "$item")"
  if [[ "$base" == "[SW.BAND]"* && "$base" != "." ]]; then
    newbase="${base/#\[SW.BAND\]/}"
    newname="$dir/$newbase"
    if [ "$item" != "$newname" ]; then
      mv -n "$item" "$newname"
    fi
  fi
done
