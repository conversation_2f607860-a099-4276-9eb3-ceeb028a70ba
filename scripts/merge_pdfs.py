import os
from PyPDF2 import PdfMerger
from pathlib import Path

# Define the directory containing the PDFs and the output path
pdf_dir = Path.home() / "Downloads" / "NHS"
output_path = Path.home() / "Downloads" / "merged_nhs_reports.pdf"

# Check if the PDF directory exists
if not pdf_dir.is_dir():
    print(f"Error: Directory not found - {pdf_dir}")
    exit(1)

# Find all PDF files in the directory
pdf_files = sorted([f for f in pdf_dir.glob("*.pdf") if f.is_file()])

if not pdf_files:
    print(f"No PDF files found in {pdf_dir}")
    exit(0)

print(f"Found {len(pdf_files)} PDF files to merge:")
for pdf_file in pdf_files:
    print(f"- {pdf_file.name}")

# Merge the PDFs
merger = PdfMerger()
for pdf_file in pdf_files:
    try:
        merger.append(str(pdf_file))
        print(f"Appended {pdf_file.name}")
    except Exception as e:
        print(f"Error appending {pdf_file.name}: {e}")
        # Optionally decide whether to continue or exit on error
        # exit(1) # Uncomment to stop on first error

# Write the merged PDF to the output file
try:
    with open(output_path, "wb") as fout:
        merger.write(fout)
    print(f"\nSuccessfully merged PDFs into: {output_path}")
except Exception as e:
    print(f"\nError writing merged PDF: {e}")
finally:
    merger.close()