import yt_dlp
import os
from pathlib import Path

def download_videos(url, output_path, cookies_file=None):
    """
    Download videos from the specified course URL
    
    Args:
        url (str): The URL of the course page
        output_path (str): Path where videos should be saved
        cookies_file (str, optional): Path to cookies file for authentication
    """
    
    # Ensure output directory exists
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # Configure yt-dlp options
    ydl_opts = {
        'format': 'best',  # Download best quality
        'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
        'cookiefile': cookies_file,
        'nooverwrites': True,
        'continuedl': True,
        'retries': 3,
    }
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([url])
            print(f"Videos downloaded successfully to {output_path}")
    except Exception as e:
        print(f"Error downloading videos: {str(e)}")

if __name__ == "__main__":
    # URL of the course content
    course_url = "https://toddvdating.com/oda/week-8-infield-2/"
    
    # Output directory for videos
    output_dir = "/Volumes/Seagate Backup Plus Drive/game/Todd-V/infields"
    
    # Path to cookies file (needed for authenticated access)
    cookies = "cookies.txt"
    
    # Start download
    if os.path.exists(cookies):
        download_videos(course_url, output_dir, cookies)
    else:
        print("Please provide cookies.txt file for authentication")