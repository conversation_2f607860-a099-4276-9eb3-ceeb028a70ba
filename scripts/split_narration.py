import os
import re

def read_text_file(file_path):
    """Read content from a text file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def split_into_chunks(content, words_per_chunk=750):
    """Split content into chunks of approximately 750 words each."""
    # Split content into sections based on headers
    sections = []
    current_section = []
    
    for line in content.split('\n'):
        if line.startswith('#'):
            if current_section:
                sections.append('\n'.join(current_section))
            current_section = [line]
        else:
            current_section.append(line)
    
    if current_section:
        sections.append('\n'.join(current_section))
    
    # Process sections into chunks
    chunks = []
    current_chunk = []
    current_word_count = 0
    
    for section in sections:
        # Always start a new chunk with a header section
        if section.strip().startswith('#'):
            if current_chunk:
                chunks.append('\n'.join(current_chunk))
                current_chunk = []
                current_word_count = 0
            current_chunk.append(section)
            continue
        
        # Split non-header sections into paragraphs
        paragraphs = [p for p in section.split('\n') if p.strip()]
        
        for paragraph in paragraphs:
            words = paragraph.split()
            word_count = len(words)
            
            if current_word_count + word_count <= words_per_chunk:
                current_chunk.append(paragraph)
                current_word_count += word_count
            else:
                # If there's room for some words, split the paragraph
                space_left = words_per_chunk - current_word_count
                if space_left > 0:
                    current_chunk.append(' '.join(words[:space_left]))
                    chunks.append('\n'.join(current_chunk))
                    current_chunk = []
                    words = words[space_left:]
                    word_count = len(words)
                else:
                    chunks.append('\n'.join(current_chunk))
                    current_chunk = []
                    current_word_count = 0
                
                # Process remaining words
                while word_count > words_per_chunk:
                    chunks.append(' '.join(words[:words_per_chunk]))
                    words = words[words_per_chunk:]
                    word_count = len(words)
                
                if words:
                    current_chunk = [' '.join(words)]
                    current_word_count = word_count
    
    # Add final chunk if there's content
    if current_chunk:
        chunks.append('\n'.join(current_chunk))
    
    return chunks

def save_chunks(chunks, output_dir):
    """Save chunks to numbered text files."""
    os.makedirs(output_dir, exist_ok=True)
    
    total_words = 0
    for i, chunk in enumerate(chunks, 1):
        # Calculate approximate duration based on word count
        words = len(re.findall(r'\w+', chunk))
        total_words += words
        duration = words / 150  # 150 words per minute
        
        filename = f"chunk_{i:03d}_{duration:.1f}min.txt"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"Chunk {i} - Approximate duration: {duration:.1f} minutes\n")
            f.write("=" * 50 + "\n\n")
            f.write(chunk)
            f.write("\n\n" + "=" * 50 + "\n")
            f.write(f"\nWord count: {words}")
    
    return total_words

def main():
    input_path = os.path.expanduser("~/Books/roman_documentary/roman_documentary.md")
    output_dir = os.path.expanduser("~/Books/roman_documentary_chunks")
    
    print("Reading content from text file...")
    content = read_text_file(input_path)
    
    print("Splitting content into chunks...")
    chunks = split_into_chunks(content)
    
    print("Saving chunks to files...")
    total_words = save_chunks(chunks, output_dir)
    
    total_duration = total_words / 150  # 150 words per minute
    print(f"\nCreated {len(chunks)} chunks:")
    print(f"Total words: {total_words}")
    print(f"Estimated total duration: {total_duration:.1f} minutes ({total_duration/60:.1f} hours)")
    print(f"\nChunks have been saved to: {output_dir}")
    print("\nEach chunk is formatted for easy reading during recording.")

if __name__ == "__main__":
    main()