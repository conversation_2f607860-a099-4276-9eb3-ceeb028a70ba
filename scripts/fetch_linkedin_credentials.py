#!/usr/bin/env python3
"""
Script to fetch LinkedIn organization ID using an existing access token.
Updates the .env file in the current directory.
"""

import json
import logging
import os
import sys
import requests
from dotenv import load_dotenv
import importlib.util

# Configure logging with more detailed format and ensure it prints to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('linkedin_credentials.log')
    ]
)

# LinkedIn API endpoints
ORG_URL = "https://api.linkedin.com/v2/organizations?q=admin"
FALLBACK_ORG_URL = "https://api.linkedin.com/v2/organizationalEntityAcls?q=roleAssignee"

def import_refresh_token():
    """Import refresh_token module from the correct path"""
    cwd = os.getcwd()
    refresh_token_path = os.path.join(cwd, 'refresh_token.py')
    spec = importlib.util.spec_from_file_location("refresh_token", refresh_token_path)
    refresh_token = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(refresh_token)
    return refresh_token

def get_organization_details(access_token: str, allow_refresh: bool = True) -> tuple:
    """
    Fetch organization details using the access token.
    Returns tuple of (organization_id, organization_name)
    """
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "X-Restli-Protocol-Version": "2.0.0"
    }
    
    logging.info(f"Using access token: {access_token[:10]}...")
    
    try:
        # First try the admin organizations endpoint
        logging.info(f"Making request to: {ORG_URL}")
        response = requests.get(ORG_URL, headers=headers)
        logging.info(f"Response status code: {response.status_code}")
        
        if response.status_code == 401 and allow_refresh:
            logging.info("Token expired, attempting to refresh...")
            refresh_token = import_refresh_token()
            new_token = refresh_token.refresh_access_token()
            logging.info("Token refreshed successfully, retrying request...")
            return get_organization_details(new_token, allow_refresh=False)
        
        response.raise_for_status()
        data = response.json()
        logging.info(f"Response data: {json.dumps(data, indent=2)}")
        
        if 'elements' in data and len(data['elements']) > 0:
            org = data['elements'][0]
            org_id = org.get('id')
            org_name = org.get('vanityName')
            logging.info(f"Found organization: {org_name} (ID: {org_id})")
            return org_id, org_name
            
        # Fallback to the organizational entity ACLs endpoint
        logging.info(f"No organizations found, trying fallback URL: {FALLBACK_ORG_URL}")
        response = requests.get(FALLBACK_ORG_URL, headers=headers)
        logging.info(f"Fallback response status code: {response.status_code}")
        
        if response.status_code == 401 and allow_refresh:
            logging.info("Token expired, attempting to refresh...")
            refresh_token = import_refresh_token()
            new_token = refresh_token.refresh_access_token()
            logging.info("Token refreshed successfully, retrying request...")
            return get_organization_details(new_token, allow_refresh=False)
            
        response.raise_for_status()
        data = response.json()
        logging.info(f"Fallback response data: {json.dumps(data, indent=2)}")
        
        if 'elements' in data and len(data['elements']) > 0:
            org_urn = data['elements'][0].get('organizationalTarget', '')
            org_id = org_urn.split(':')[-1]  # Extract ID from URN
            logging.info(f"Found organization ID from fallback: {org_id}")
            return org_id, None
            
        raise Exception("No organizations found in either response")
            
    except Exception as e:
        logging.error(f"Error getting organization details: {str(e)}")
        if isinstance(e, requests.exceptions.RequestException) and hasattr(e, 'response'):
            logging.error(f"Full response text: {e.response.text}")
        raise

def update_env_file(org_id: str, env_path: str):
    """Update the .env file with the organization ID"""
    logging.info(f"Updating .env file at: {env_path}")
    
    # Read existing .env content
    env_content = {}
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    env_content[key] = value
    
    # Update organization ID
    env_content['LINKEDIN_ORGANIZATION_ID'] = org_id
    
    # Write back to .env while preserving other variables
    with open(env_path, 'w') as f:
        for key, value in env_content.items():
            f.write(f"{key}={value}\n")
    
    logging.info(f"Updated .env file with organization ID: {org_id}")

def main():
    """Main script execution"""
    # Get the absolute path to the project root directory
    cwd = os.getcwd()
    env_path = os.path.join(cwd, '.env')
    
    logging.info(f"Current working directory: {cwd}")
    logging.info(f"Using .env file at: {env_path}")
    
    # Load existing .env from the project root directory
    load_dotenv(env_path)
    
    # Get access token from .env
    access_token = os.getenv('LINKEDIN_ACCESS_TOKEN')
    if not access_token:
        logging.error("LINKEDIN_ACCESS_TOKEN must be set in .env")
        sys.exit(1)
    
    # Remove any accidental spaces in the access token
    access_token = access_token.strip()
    
    try:
        # Get organization details
        org_id, org_name = get_organization_details(access_token)
        if org_name:
            logging.info(f"Successfully obtained organization ID for {org_name}")
        else:
            logging.info(f"Successfully obtained organization ID: {org_id}")
        
        # Update .env file
        update_env_file(org_id, env_path)
        logging.info("Successfully updated .env file!")
        
    except Exception as e:
        logging.error(f"Failed to fetch organization details: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()