import requests

def get_multi_price():
    # Test endpoint for multiple symbols
    url = "https://min-api.cryptocompare.com/data/pricemultifull"
    params = {
        "fsyms": "HBAR,SUI,BTC,ETH",  # Multiple symbols
        "tsyms": "USD",  # Convert to USD
        # Note: Using demo API key - rate limited but works for testing
        "api_key": "aa92787c98e94cbc4cac4468b2de3d3e876cbca3e4b19ec0d12ac0f1c86e96bf"
    }
    
    response = requests.get(url, params=params)
    print(f"Response status: {response.status_code}")
    print("Response data:")
    data = response.json()
    if "Response" in data and data["Response"] == "Error":
        print(f"Error: {data['Message']}")
    else:
        print("RAW Prices:")
        if "RAW" in data:
            for crypto in data["RAW"]:
                price = data["RAW"][crypto]["USD"]["PRICE"]
                print(f"{crypto}: ${price}")

if __name__ == "__main__":
    get_multi_price()