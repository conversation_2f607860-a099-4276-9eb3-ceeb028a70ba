/**
 * Configuration object - Add your API key here
 */
const CONFIG = {
  API_KEY: '70069f4c5c8997eeb83d48baf322ff408f53d3ed7d0cb8d39ac4f0da41c05547', // Get from https://www.cryptocompare.com/cryptopian/api-keys
  SHEET_NAME: 'Crypto',
  SYMBOL_COLUMN: 'B',
  PRICE_COLUMN: 'E',
  START_ROW: 2
};

/**
 * Updates cryptocurrency prices in the spreadsheet.
 * This function can be set up as a time-driven trigger to run periodically.
 */
function updateCryptoPrices() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${CONFIG.SHEET_NAME}" not found`);
    }

    const lastRow = sheet.getLastRow();
    if (lastRow < CONFIG.START_ROW) {
      Logger.log('No data to process');
      return;
    }

    // Get all symbols at once
    const symbols = sheet.getRange(`${CONFIG.SYMBOL_COLUMN}${CONFIG.START_ROW}:${CONFIG.SYMBOL_COLUMN}${lastRow}`).getValues();
    
    // Process each symbol
    symbols.forEach((symbolCell, index) => {
      const symbol = symbolCell[0].toString().trim();
      if (!symbol) return; // Skip empty cells
      
      try {
        const gbpPrice = fetchCryptoPrice(symbol);
        const row = CONFIG.START_ROW + index;
        sheet.getRange(`${CONFIG.PRICE_COLUMN}${row}`).setValue(gbpPrice);
        
        // Add a small delay to avoid hitting rate limits
        Utilities.sleep(100);
      } catch (error) {
        Logger.log(`Error processing ${symbol}: ${error.message}`);
        // Optionally write error to the sheet
        sheet.getRange(`${CONFIG.PRICE_COLUMN}${CONFIG.START_ROW + index}`).setValue('ERROR');
      }
    });
    
    // Update last refresh timestamp
    sheet.getRange('A1').setValue(`Last Updated: ${new Date().toLocaleString()}`);
    
  } catch (error) {
    Logger.log(`Main error: ${error.message}`);
    throw error;
  }
}

/**
 * Fetches the current price for a given cryptocurrency in GBP.
 * 
 * @param {string} symbol - The cryptocurrency symbol (e.g., 'BTC', 'ETH', 'DOT')
 * @returns {number} The current price in GBP
 */
function fetchCryptoPrice(symbol) {
  const url = `https://min-api.cryptocompare.com/data/price?fsym=${symbol}&tsyms=GBP&api_key=${CONFIG.API_KEY}`;
  
  const response = UrlFetchApp.fetch(url);
  const data = JSON.parse(response.getContentText());
  
  if (data.Response === 'Error') {
    throw new Error(data.Message || 'API Error');
  }
  
  if (!data.GBP) {
    throw new Error('Price data not available');
  }
  
  return data.GBP;
}

/**
 * Creates a time-driven trigger to update prices automatically.
 * By default, it will run every hour.
 */
function createHourlyTrigger() {
  // Delete any existing triggers
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => ScriptApp.deleteTrigger(trigger));
  
  // Create new trigger
  ScriptApp.newTrigger('updateCryptoPrices')
    .timeBased()
    .everyHours(1)
    .create();
  
  Logger.log('Hourly trigger created successfully');
}