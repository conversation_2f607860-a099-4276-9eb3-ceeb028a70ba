import yt_dlp
import os

def download_audio(video_url, output_path=None):
    """Download audio from YouTube video using yt-dlp."""
    try:
        if not output_path:
            output_path = os.getcwd()
        output_path = os.path.expanduser(output_path)  # Expand ~ to full path
        
        # Configure yt-dlp options
        ydl_opts = {
            'format': 'bestaudio/best',
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }],
            'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
            'verbose': True
        }
        
        # Download audio
        print(f"Downloading audio to {output_path}...")
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([video_url])
            
        print("\nDownload complete!")
        return True
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    # Download directly to ~/Downloads
    video_url = "https://www.youtube.com/watch?v=pzBvb4umRzo"
    download_path = "~/Downloads"
    download_audio(video_url, download_path)