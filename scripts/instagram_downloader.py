import os
import instaloader
import re
from pathlib import Path

def extract_cookies(cookie_string):
    cookies = {}
    pairs = cookie_string.split(';')
    
    # Process pairs in reverse order to keep the last occurrence of each cookie
    for pair in reversed(pairs):
        pair = pair.strip()
        if '=' in pair:
            key, value = pair.split('=', 1)
            key = key.strip()
            # Only set the cookie if we haven't seen it before (since we're going backwards)
            if key not in cookies:
                cookies[key] = value.strip()
    return cookies

def download_instagram_profile():
    # Setup paths
    env_path = os.path.expanduser("~/Documents/Sush/.env")
    download_path = os.path.expanduser("~/Documents/Sush")
    
    # Create directory if it doesn't exist
    os.makedirs(download_path, exist_ok=True)
    
    # Initialize instaloader
    L = instaloader.Instaloader(
        dirname_pattern=os.path.join(download_path, "{target}"),
        download_videos=True,
        download_video_thumbnails=False,
        download_geotags=False,
        download_comments=False,
        save_metadata=False,
        post_metadata_txt_pattern="",
        compress_json=False
    )
    
    try:
        # Read the raw cookie value from .env file
        with open(env_path, 'r') as f:
            cookie_string = f.read().strip()
        
        if not cookie_string:
            raise ValueError("Cookie value is empty in .env file")
        
        # Parse cookies
        cookies = extract_cookies(cookie_string)
        
        if 'sessionid' not in cookies:
            raise ValueError("sessionid cookie not found")
            
        # Set sessionid cookie
        L.context._session.cookies.set('sessionid', cookies['sessionid'], domain='.instagram.com')
        
        # Set other important cookies
        for cookie_name in ['ds_user_id', 'csrftoken']:
            if cookie_name in cookies:
                L.context._session.cookies.set(cookie_name, cookies[cookie_name], domain='.instagram.com')
        
        # Download profile
        profile = instaloader.Profile.from_username(L.context, "sushi.garikapati")
        print(f"Downloading media for profile: {profile.username}")
        print(f"Found {profile.mediacount} posts")
        
        # Download posts one by one to handle any potential issues
        for post in profile.get_posts():
            try:
                print(f"Downloading post {post.mediaid}")
                L.download_post(post, target=profile.username)
            except Exception as post_error:
                print(f"Error downloading post {post.mediaid}: {str(post_error)}")
                continue
        
        print("Download completed successfully!")
        
    except FileNotFoundError:
        print("Error: .env file not found in ~/Documents/Sush/. Please make sure it exists.")
    except instaloader.exceptions.ConnectionException:
        print("Error: Failed to connect to Instagram. Please check your internet connection.")
    except instaloader.exceptions.BadCredentialsException as e:
        print(f"Error: Authentication failed. {str(e)}")
    except instaloader.exceptions.ProfileNotExistsException:
        print("Error: The specified Instagram profile does not exist.")
    except Exception as e:
        print(f"An unexpected error occurred: {str(e)}")

if __name__ == "__main__":
    download_instagram_profile()