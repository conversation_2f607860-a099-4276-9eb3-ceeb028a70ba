import unittest
import requests
from crypto_price import get_crypto_price

class TestCryptoPrice(unittest.TestCase):
    def test_get_crypto_price(self):
        # Test fetching DOT price in GBP
        price = get_crypto_price('DOT', 'GBP')
        self.assertIsInstance(price, float)
        self.assertGreater(price, 0)

    def test_invalid_crypto(self):
        # Test with invalid cryptocurrency
        with self.assertRaises(ValueError):
            get_crypto_price('INVALID_CRYPTO', 'GBP')

    def test_invalid_currency(self):
        # Test with invalid currency
        with self.assertRaises(ValueError):
            get_crypto_price('DOT', 'INVALID_CURRENCY')

if __name__ == '__main__':
    unittest.main()