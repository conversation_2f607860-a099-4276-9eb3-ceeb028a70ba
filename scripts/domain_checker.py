import whois
from itertools import product
import time

# List of creative 6-letter verb-like names
names = [
    "nextra", "valora", "dynami", "synthi", "logica", "quanta",
    "techna", "innova", "futura", "zenith", "optima", "cognix",
    "ultima", "vector", "metric", "photon", "neuron", "cipher",
    "propel", "vertex", "matrix", "praxis", "nexius", "syntex",
    "modula", "digita", "stellr", "pulsra", "quantx", "cosmir",
    "centri", "morpho", "axiona", "noetic", "lumina", "kinetx",
    "gravix", "atomix", "cognos", "equinx", "helios", "nexion",
    "orbita", "prisma", "quorum", "sonara", "tensra", "vortex",
    "xentra", "zephyr"
]

def check_domain(domain):
    try:
        w = whois.whois(domain)
        return w.domain_name is None
    except Exception:
        return True  # If whois fails, domain might be available
    
def main():
    available_domains = []
    
    for name in names:
        domain = f"{name}.ai"
        print(f"Checking {domain}...")
        
        if check_domain(domain):
            available_domains.append(domain)
            print(f"✓ {domain} might be available!")
        else:
            print(f"✗ {domain} is taken")
            
        time.sleep(1)  # Be nice to whois servers
        
    print("\nPotentially available domains:")
    for domain in available_domains:
        print(domain)

if __name__ == "__main__":
    main()