const http = require('http');

const server = http.createServer((req, res) => {
    const code = new URL(req.url, `http://${req.headers.host}`).searchParams.get('code');
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
        <h1>Authorization Code Received</h1>
        <p>Your code is: ${code}</p>
    `);
});

server.listen(8080, () => {
    console.log('Server running at http://localhost:8080/');
});
