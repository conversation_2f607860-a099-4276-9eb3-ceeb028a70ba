import os
from ebooklib import epub
import glob

def clean_content(content):
    """Clean the chunk content by removing headers and footers."""
    lines = content.split('\n')
    clean_lines = []
    recording = False
    
    for line in lines:
        if '=' * 50 in line:
            if not recording:
                recording = True
                continue
            else:
                break
        if recording and line.strip() and 'Word count:' not in line:
            if line.startswith('#'):
                # Convert markdown headers to HTML
                level = len(line.split()[0])  # Count the number of '#'
                text = line.lstrip('#').strip()
                clean_lines.append(f'<h{level}>{text}</h{level}>')
            else:
                clean_lines.append(f'<p>{line}</p>')
    
    return '\n'.join(clean_lines)

def create_complete_html():
    """Create a single HTML document with all content."""
    chunk_dir = os.path.expanduser("~/Books/roman_documentary_chunks")
    chunk_files = sorted(glob.glob(os.path.join(chunk_dir, "chunk_*.txt")))
    
    print(f"Processing {len(chunk_files)} chunks...")
    
    content = '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops">
<head>
    <title>Life in Ancient Rome: A Cultural Journey</title>
    <link rel="stylesheet" type="text/css" href="style.css" />
</head>
<body>
    <h1>Life in Ancient Rome: A Cultural Journey</h1>
'''
    
    for i, chunk_file in enumerate(chunk_files, 1):
        print(f"Processing chunk {i}...")
        with open(chunk_file, 'r', encoding='utf-8') as f:
            chunk_content = f.read()
            content += f'\n<div class="chapter">\n{clean_content(chunk_content)}\n</div>\n'
    
    content += '</body>\n</html>'
    return content

def main():
    # Create EPUB book
    book = epub.EpubBook()
    
    # Set metadata
    book.set_identifier('roman-documentary')
    book.set_title('Life in Ancient Rome: A Cultural Journey')
    book.set_language('en')
    book.add_author('Roman History Documentary')
    
    # Add CSS
    style = '''
body {
    margin: 5%;
    text-align: justify;
    font-family: "Times New Roman", serif;
}
h1 {
    text-align: center;
    margin-bottom: 2em;
}
h2 {
    margin-top: 2em;
}
h3 {
    margin-top: 1em;
}
p {
    margin: 1em 0;
    line-height: 1.5;
}
.chapter {
    margin-bottom: 2em;
}
'''
    css = epub.EpubItem(
        uid="style",
        file_name="style.css",
        media_type="text/css",
        content=style
    )
    book.add_item(css)
    
    # Create main document
    main_doc = epub.EpubHtml(
        title='Life in Ancient Rome',
        file_name='content.xhtml',
        content=create_complete_html(),
        lang='en'
    )
    main_doc.add_item(css)
    book.add_item(main_doc)
    
    # Add to book
    book.spine = ['nav', main_doc]
    book.add_item(epub.EpubNcx())
    book.add_item(epub.EpubNav())
    
    # Write EPUB file
    epub_path = os.path.expanduser("~/Books/roman_documentary_script.epub")
    print(f"Creating EPUB file at: {epub_path}")
    epub.write_epub(epub_path, book, {})
    print("EPUB file created successfully!")

if __name__ == '__main__':
    main()