def solve_robot_challenge(sequence_str):
    # Parse input sequence
    sequence = []
    for item in sequence_str.split(', '):
        robot, button = item.split()
        sequence.append((robot, int(button)))
    
    # Initialize robot states
    orange_pos = blue_pos = 1
    orange_target = blue_target = None
    sequence_idx = 0
    time = 0
    output = []
    
    while sequence_idx < len(sequence):
        time += 1
        current_robot, current_button = sequence[sequence_idx]
        
        # Determine targets
        if not orange_target:
            for i in range(sequence_idx, len(sequence)):
                if sequence[i][0] == 'O':
                    orange_target = sequence[i][1]
                    break
        if not blue_target:
            for i in range(sequence_idx, len(sequence)):
                if sequence[i][0] == 'B':
                    blue_target = sequence[i][1]
                    break
        
        # Determine actions
        orange_action = None
        blue_action = None
        
        # Orange robot logic
        if orange_pos == orange_target and current_robot == 'O':
            orange_action = f"Push button {orange_pos}"
            sequence_idx += 1
            orange_target = None
        elif orange_pos < orange_target:
            orange_pos += 1
            orange_action = f"Move to button {orange_pos}"
        elif orange_pos > orange_target:
            orange_pos -= 1
            orange_action = f"Move to button {orange_pos}"
        else:
            orange_action = f"Stay at button {orange_pos}"
        
        # Blue robot logic
        if blue_pos == blue_target and current_robot == 'B':
            # Only press if orange isn't pressing this second
            if not (orange_pos == orange_target and current_robot == 'O' and sequence_idx < len(sequence) and sequence[sequence_idx][0] == 'O'):
                blue_action = f"Push button {blue_pos}"
                sequence_idx += 1
                blue_target = None
            else:
                blue_action = f"Stay at button {blue_pos}"
        elif blue_pos < blue_target:
            blue_pos += 1
            blue_action = f"Move to button {blue_pos}"
        elif blue_pos > blue_target:
            blue_pos -= 1
            blue_action = f"Move to button {blue_pos}"
        else:
            blue_action = f"Stay at button {blue_pos}"
        
        output.append((time, orange_action, blue_action))
    
    return output

def test_case_1():
    sequence = "O 2, B 1, B 2, O 4"
    result = solve_robot_challenge(sequence)
    
    expected = [
        (1, "Move to button 2", "Stay at button 1"),
        (2, "Push button 2", "Stay at button 1"),
        (3, "Move to button 3", "Push button 1"),
        (4, "Move to button 4", "Move to button 2"),
        (5, "Stay at button 4", "Push button 2"),
        (6, "Push button 4", "Stay at button 2")
    ]
    
    assert len(result) == len(expected), f"Expected {len(expected)} steps, got {len(result)}"
    for (res, exp) in zip(result, expected):
        assert res == exp, f"At time {exp[0]}, expected Orange: {exp[1]}, Blue: {exp[2]}, got Orange: {res[1]}, Blue: {res[2]}"

def test_case_2():
    sequence = "O 5, O 8, B 100"
    result = solve_robot_challenge(sequence)
    
    # Verify first few and last steps
    assert result[0] == (1, "Move to button 2", "Move to button 2")
    assert result[1] == (2, "Move to button 3", "Move to button 3")
    assert result[2] == (3, "Move to button 4", "Move to button 4")
    assert result[3] == (4, "Move to button 5", "Move to button 5")
    assert result[4] == (5, "Push button 5", "Move to button 6")
    assert result[-2] == (99, "Stay at button 8", "Move to button 100")
    assert result[-1] == (100, "Stay at button 8", "Push button 100")
    
    assert len(result) == 100, f"Expected 100 steps, got {len(result)}"

def test_case_3():
    sequence = "B 2, B 1"
    result = solve_robot_challenge(sequence)
    
    expected = [
        (1, "Stay at button 1", "Move to button 2"),
        (2, "Stay at button 1", "Push button 2"),
        (3, "Stay at button 1", "Move to button 1"),
        (4, "Stay at button 1", "Push button 1")
    ]
    
    assert len(result) == len(expected), f"Expected {len(expected)} steps, got {len(result)}"
    for (res, exp) in zip(result, expected):
        assert res == exp, f"At time {exp[0]}, expected Orange: {exp[1]}, Blue: {exp[2]}, got Orange: {res[1]}, Blue: {res[2]}"

if __name__ == "__main__":
    test_case_1()
    test_case_2()
    test_case_3()
    print("All test cases passed!")