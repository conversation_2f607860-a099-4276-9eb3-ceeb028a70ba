import os
import requests

def get_crypto_price(crypto_symbol: str, currency: str, api_key: str = None) -> float:
    """
    Fetch the current price of a cryptocurrency in the specified currency.
    
    Args:
        crypto_symbol (str): The symbol of the cryptocurrency (e.g., 'BTC', 'ETH', 'DOT')
        currency (str): The currency to get the price in (e.g., 'USD', 'GBP', 'EUR')
        api_key (str, optional): CryptoCompare API key. If not provided, will look for CRYPTOCOMPARE_API_KEY env variable
    
    Returns:
        float: The current price of the cryptocurrency
    
    Raises:
        ValueError: If the cryptocurrency or currency is invalid, or if no API key is available
        requests.RequestException: If there's an error connecting to the API
    """
    # Get API key from parameter or environment
    api_key = api_key or os.environ.get('CRYPTOCOMPARE_API_KEY')
    if not api_key:
        raise ValueError("API key is required. Either pass it as a parameter or set CRYPTOCOMPARE_API_KEY environment variable")
    
    # Convert symbols to uppercase
    crypto_symbol = crypto_symbol.upper()
    currency = currency.upper()
    
    # Construct the API URL with API key
    url = f"https://min-api.cryptocompare.com/data/price?fsym={crypto_symbol}&tsyms={currency}&api_key={api_key}"
    
    # Make the API request
    try:
        response = requests.get(url)
        response.raise_for_status()  # Raise an exception for bad status codes
        data = response.json()
        
        # Check if the response contains an error
        if "Response" in data and data["Response"] == "Error":
            raise ValueError(data.get("Message", "Invalid cryptocurrency or currency"))
        
        # Check if the currency exists in the response
        if currency not in data:
            raise ValueError(f"Currency {currency} not found in response")
        
        price = data[currency]
        return float(price)
        
    except requests.RequestException as e:
        raise requests.RequestException(f"Error fetching price: {str(e)}")

if __name__ == "__main__":
    try:
        # Example usage
        api_key = os.environ.get('CRYPTOCOMPARE_API_KEY')
        if not api_key:
            print("Please set your CRYPTOCOMPARE_API_KEY environment variable")
            print("Get your API key from: https://www.cryptocompare.com/cryptopian/api-keys")
            exit(1)
            
        price = get_crypto_price("DOT", "GBP", api_key)
        print(f"Current DOT price in GBP: £{price:.2f}")
    except (ValueError, requests.RequestException) as e:
        print(f"Error: {str(e)}")