import os
import glob

def clean_content(content):
    """Clean the chunk content by removing headers and footers."""
    lines = content.split('\n')
    clean_lines = []
    recording = False
    
    for line in lines:
        if '=' * 50 in line:
            if not recording:
                recording = True
                continue
            else:
                break
        if recording and line.strip() and 'Word count:' not in line:
            clean_lines.append(line)
    
    return '\n'.join(clean_lines)

def main():
    chunk_dir = os.path.expanduser("~/Books/roman_documentary_chunks")
    output_md = os.path.expanduser("~/Books/roman_documentary.md")
    chunk_files = sorted(glob.glob(os.path.join(chunk_dir, "chunk_*.txt")))
    
    print(f"Processing {len(chunk_files)} chunks...")
    
    # Start with metadata block
    content = """---
title: Life in Ancient Rome - A Cultural Journey
author: Roman History Documentary
rights: Public Domain
language: en-US
---

# Life in Ancient Rome: A Cultural Journey

"""
    
    # Process each chunk
    for i, chunk_file in enumerate(chunk_files, 1):
        print(f"Processing chunk {i}...")
        with open(chunk_file, 'r', encoding='utf-8') as f:
            chunk_content = clean_content(f.read())
            content += chunk_content + "\n\n"
    
    # Write the markdown file
    with open(output_md, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Markdown file created successfully!")
    print("Now run: pandoc -o ~/Books/roman_documentary_script.epub ~/Books/roman_documentary.md --toc --toc-depth=3")

if __name__ == '__main__':
    main()