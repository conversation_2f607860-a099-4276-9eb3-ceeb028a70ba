import requests
import time
from uuid import uuid4

base_url = "http://localhost:8000/crypto/sign"
webhook_url = "https://bright-honey-03.webhook.cool"

# Number of requests and duration
total_requests = 60
duration_seconds = 60

# Calculate the delay between requests
delay = duration_seconds / total_requests

def send_request(message):
    url = f"{base_url}?message={message}&webhook_url={webhook_url}"
    response = requests.get(url)
    print(f"Request sent with message: {message}")
    print(f"Response: {response.text}")

start_time = time.time()

for i in range(total_requests):
    # Generate a unique message using UUID
    unique_message = f"msg_{uuid4().hex[:8]}"
    
    # Send the request
    send_request(unique_message)
    
    # Calculate the time elapsed
    elapsed_time = time.time() - start_time
    
    # Calculate the time to wait before the next request
    time_to_wait = (i + 1) * delay - elapsed_time
    
    # If there's time left, wait
    if time_to_wait > 0:
        time.sleep(time_to_wait)

end_time = time.time()
total_time = end_time - start_time
print(f"Total time taken: {total_time:.2f} seconds")
