#!/bin/bash
set -ex # Exit on error and print commands

BASE="/Users/<USER>/Videos/Courses/ETJ_English_The_British_Pronunciation_Programme_2020"
cd "$BASE" || { echo "Failed to cd into $BASE"; exit 1; }

echo "Starting renaming process in $(pwd)"

# Function to find and rename a directory robustly
rename_dir() {
  local current_name_pattern="$1" # Pattern to find the current dir (e.g., "Introduction*")
  local new_name="$2"             # The desired new name (e.g., "01_Introduction")
  local found_dir

  # Find the directory matching the pattern (case-insensitive, max depth 1)
  # Use -print -quit to stop after the first match
  found_dir=$(find . -maxdepth 1 -type d -iname "$current_name_pattern" -print -quit)

  # Proceed only if a directory was found
  if [ -n "$found_dir" ] && [ -d "$found_dir" ]; then
    # Check if it's already the target name (ignoring './' prefix from find)
    if [[ "$found_dir" != "./$new_name" ]]; then
      echo "Renaming '$found_dir' to '$new_name'..."
      # Use the exact path found by 'find'
      mv "$found_dir" "$new_name"
    else
      echo "'$found_dir' is already named '$new_name' or similar. Skipping."
    fi
  else
    echo "Warning: Directory matching pattern '$current_name_pattern' not found or already renamed. Skipping."
  fi
}

# --- Rename based on expected patterns ---
# Use patterns that are likely unique enough to find the correct folder
rename_dir "Introduction" "01_Introduction"
rename_dir "Stress and Intonation" "02_Stress_and_Intonation"
rename_dir "Introduction to Vowels*" "03_Introduction_to_Vowels_SCHWA_and_Stress" # Using wildcard
rename_dir "Vowels" "04_Vowels"
rename_dir "Diphthongs" "05_Diphthongs"
rename_dir "Consonants" "06_Consonants"
rename_dir "Consonant Clusters" "07_Consonant_Clusters"
rename_dir "Connected speech" "08_Connected_Speech"
rename_dir "Listening Practice" "09_Listening_Practice"
# Check 10_Congratulations just in case, but expect it to be skipped
rename_dir "10_Congratulations" "10_Congratulations"

echo "Renaming process finished."
set +x # Disable command printing