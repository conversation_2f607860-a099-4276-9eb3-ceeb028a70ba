#!/bin/bash
set -ex # Exit on error and print commands

BASE="/Users/<USER>/Videos/Courses/ETJ_English_The_British_Pronunciation_Programme_2020"
cd "$BASE" || { echo "Failed to cd into $BASE"; exit 1; }

echo "Starting renaming process in $(pwd)"

# --- Rename based on exact names from ls output ---

# Check if source exists before attempting rename
if [ -d "Introduction" ]; then
  echo "Renaming 'Introduction'..."
  mv "Introduction" "01_Introduction"
else
  echo "Directory 'Introduction' not found or already renamed."
fi

if [ -d "Stress and Intonation" ]; then
  echo "Renaming 'Stress and Intonation'..."
  mv "Stress and Intonation" "02_Stress_and_Intonation"
else
  echo "Directory 'Stress and Intonation' not found or already renamed."
fi

if [ -d "Introduction to Vowels, SCHWA and Stress" ]; then
  echo "Renaming 'Introduction to Vowels, SCHWA and Stress'..."
  mv "Introduction to Vowels, SCHWA and Stress" "03_Introduction_to_Vowels_SCHWA_and_Stress"
else
  echo "Directory 'Introduction to Vowels, SCHWA and Stress' not found or already renamed."
fi

if [ -d "Vowels" ]; then
  echo "Renaming 'Vowels'..."
  mv "Vowels" "04_Vowels"
else
  echo "Directory 'Vowels' not found or already renamed."
fi

if [ -d "Diphthongs" ]; then
  echo "Renaming 'Diphthongs'..."
  mv "Diphthongs" "05_Diphthongs"
else
  echo "Directory 'Diphthongs' not found or already renamed."
fi

if [ -d "Consonants" ]; then
  echo "Renaming 'Consonants'..."
  mv "Consonants" "06_Consonants"
else
  echo "Directory 'Consonants' not found or already renamed."
fi

if [ -d "Consonant Clusters" ]; then
  echo "Renaming 'Consonant Clusters'..."
  mv "Consonant Clusters" "07_Consonant_Clusters"
else
  echo "Directory 'Consonant Clusters' not found or already renamed."
fi

# Note: 'Connected speech' from ls output has lowercase 's'
if [ -d "Connected speech" ]; then
  echo "Renaming 'Connected speech'..."
  mv "Connected speech" "08_Connected_Speech"
else
  echo "Directory 'Connected speech' not found or already renamed."
fi

if [ -d "Listening Practice" ]; then
  echo "Renaming 'Listening Practice'..."
  mv "Listening Practice" "09_Listening_Practice"
else
  echo "Directory 'Listening Practice' not found or already renamed."
fi

# 10_Congratulations already exists - check added for completeness
if [ -d "10_Congratulations" ]; then
   echo "Directory '10_Congratulations' already exists with the correct name."
else
   # This case should ideally not happen based on ls output
   echo "Warning: Expected '10_Congratulations' was not found."
fi


echo "Renaming process finished."
set +x # Disable command printing