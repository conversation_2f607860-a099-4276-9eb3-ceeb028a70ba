/**
 * Configuration object - Add your API key here
 */
const CONFIG = {
  API_KEY: '1abb1a02ba951a59141238abdafe8af60faba2503e749eab3d5788ff2e390796',
  SHEET_NAME: 'Crypto',
  SYMBOL_COLUMN: 'B',
  PRICE_COLUMN: 'E',
  START_ROW: 2
};

/**
 * Updates cryptocurrency prices in the spreadsheet.
 * This function can be set up as a time-driven trigger to run periodically.
 */
function updateCryptoPrices() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${CONFIG.SHEET_NAME}" not found`);
    }

    const lastRow = sheet.getLastRow();
    if (lastRow < CONFIG.START_ROW) {
      Logger.log('No data to process');
      return;
    }

    // Get all symbols at once
    const symbols = sheet.getRange(`${CONFIG.SYMBOL_COLUMN}${CONFIG.START_ROW}:${CONFIG.SYMBOL_COLUMN}${lastRow}`).getValues();
    
    // Filter out empty cells and create comma-separated list
    const validSymbols = symbols
      .map(cell => cell[0].toString().trim())
      .filter(symbol => symbol !== '');

    if (validSymbols.length === 0) {
      Logger.log('No valid symbols found');
      return;
    }

    // Fetch all prices in a single API call
    const priceData = fetchCryptoPrices(validSymbols);
    
    // Update prices in the sheet
    symbols.forEach((symbolCell, index) => {
      const symbol = symbolCell[0].toString().trim();
      if (!symbol) return; // Skip empty cells
      
      const row = CONFIG.START_ROW + index;
      try {
        const price = priceData[symbol];
        sheet.getRange(`${CONFIG.PRICE_COLUMN}${row}`).setValue(price);
      } catch (error) {
        Logger.log(`Error processing ${symbol}: ${error.message}`);
        sheet.getRange(`${CONFIG.PRICE_COLUMN}${row}`).setValue('ERROR');
      }
    });
    
    // Update last refresh timestamp
    sheet.getRange('A1').setValue(`Last Updated: ${new Date().toLocaleString()}`);
    
  } catch (error) {
    Logger.log(`Main error: ${error.message}`);
    throw error;
  }
}

/**
 * Fetches current prices for multiple cryptocurrencies in a single API call.
 * 
 * @param {string[]} symbols - Array of cryptocurrency symbols
 * @returns {Object} Object mapping symbols to their GBP prices
 */
function fetchCryptoPrices(symbols) {
  const symbolsString = symbols.join(',');
  const url = `https://min-api.cryptocompare.com/data/pricemultifull?fsyms=${symbolsString}&tsyms=GBP&api_key=${CONFIG.API_KEY}`;
  
  const response = UrlFetchApp.fetch(url);
  const data = JSON.parse(response.getContentText());
  
  if (data.Response === 'Error') {
    throw new Error(data.Message || 'API Error');
  }
  
  if (!data.RAW) {
    throw new Error('Price data not available');
  }
  
  // Create a map of symbol to price
  const prices = {};
  symbols.forEach(symbol => {
    if (data.RAW[symbol] && data.RAW[symbol].GBP) {
      prices[symbol] = data.RAW[symbol].GBP.PRICE;
    } else {
      prices[symbol] = 'N/A';
    }
  });
  
  return prices;
}

/**
 * Creates a time-driven trigger to update prices automatically.
 * By default, it will run every hour.
 */
function createHourlyTrigger() {
  // Delete any existing triggers
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => ScriptApp.deleteTrigger(trigger));
  
  // Create new trigger
  ScriptApp.newTrigger('updateCryptoPrices')
    .timeBased()
    .everyHours(1)
    .create();
  
  Logger.log('Hourly trigger created successfully');
}